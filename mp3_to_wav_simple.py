#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单版本：MP3转WAV格式转换器
需要安装：pip install pydub
"""

from pydub import AudioSegment
import os
import sys

def convertMp3ToWav(mp3_file, wav_file=None):
    """
    将MP3文件转换为WAV格式
    
    Args:
        mp3_file (str): 输入的MP3文件路径
        wav_file (str): 输出的WAV文件路径，如果为None则自动生成
    
    Returns:
        bool: 转换成功返回True，失败返回False
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(mp3_file):
            print(f"错误：文件 {mp3_file} 不存在")
            return False
        
        # 如果没有指定输出文件名，自动生成
        if wav_file is None:
            wav_file = os.path.splitext(mp3_file)[0] + '.wav'
        
        print(f"正在转换: {mp3_file} -> {wav_file}")
        
        # 加载MP3文件
        audio = AudioSegment.from_mp3(mp3_file)
        
        # 导出为WAV格式
        audio.export(wav_file, format="wav")
        
        print(f"转换完成: {wav_file}")
        return True
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python mp3_to_wav_simple.py <mp3文件路径> [wav文件路径]")
        print("示例: python mp3_to_wav_simple.py music.mp3")
        print("示例: python mp3_to_wav_simple.py music.mp3 output.wav")
        return
    
    mp3_file = sys.argv[1]
    wav_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = convertMp3ToWav(mp3_file, wav_file)
    
    if success:
        print("转换成功！")
    else:
        print("转换失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
