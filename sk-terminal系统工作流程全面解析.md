# sk-terminal系统工作流程全面解析

## 🎯 **系统架构概览**

sk-terminal是一个基于ESP32的智能音频终端系统，实现了完整的音频采集、处理、编码、网络传输和状态管理功能。系统采用事件驱动架构，通过状态机控制和回调机制实现模块间的协调工作。

### **核心技术特点**
- **多任务并发处理**: 基于FreeRTOS的多任务架构
- **事件驱动设计**: 状态机 + 回调机制
- **实时音频处理**: 低延迟音频采集、编码和播放
- **网络通信**: WiFi + WebSocket + TCP协议栈
- **智能语音识别**: AFE + 唤醒词检测 + 命令识别
- **传感器集成**: 陀螺仪、温湿度传感器等

## 📊 **系统架构图**

```mermaid
graph TB
    subgraph "硬件层"
        A1[ES8311 DAC] 
        A2[ES7210 ADC]
        A3[ICM42607 陀螺仪]
        A4[WS2812 LED]
        A5[按键]
    end
    
    subgraph "驱动层"
        B1[AudioDev]
        B2[I2C驱动]
        B3[RMT LED驱动]
        B4[GPIO驱动]
    end
    
    subgraph "音频处理层"
        C1[SkRecorder录音]
        C2[SkPlayer播放]
        C3[SkSr语音识别]
        C4[SkOpusEnc编码]
        C5[SkOpusDec解码]
        C6[AFE音频前端]
    end
    
    subgraph "网络通信层"
        D1[SkWifi WiFi管理]
        D2[SkWebSocket客户端]
        D3[SkClink控制链路]
        D4[SkRlink数据链路]
    end
    
    subgraph "应用控制层"
        E1[状态机SkSm]
        E2[MyApp应用逻辑]
        E3[SkConfig配置管理]
        E4[SkOta升级管理]
    end
    
    subgraph "系统服务层"
        F1[SkOs系统服务]
        F2[内存管理]
        F3[任务调度]
        F4[定时器服务]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B2
    A4 --> B3
    A5 --> B4
    
    B1 --> C1
    B1 --> C2
    C1 --> C6
    C6 --> C3
    C1 --> C4
    C5 --> C2
    
    D1 --> D2
    D2 --> E2
    D3 --> E1
    D4 --> C4
    
    E1 --> E2
    E1 --> E3
    E1 --> E4
    
    F1 --> E1
    F2 --> C1
    F3 --> C1
    F4 --> E1
```

## 🔄 **状态机控制机制**

### **状态定义**
```c
enum {
    STATE_INIT = 0,      // 初始化状态
    STATE_IDLE,          // 空闲状态
    STATE_CONNECTING,    // 网络连接状态
    STATE_CALL,          // 通话状态
    STATE_MUSIC,         // 音乐播放状态
    STATE_CONFIG,        // 配置状态
    STATE_QUERY,         // 查询状态
    STATE_HELP,          // 帮助状态
    STATE_OTA,           // OTA升级状态
    STATE_PM,            // 电源管理状态
    STATE_REBOOT,        // 重启状态
    STATE_MAX
};
```

### **事件类型**
```c
enum {
    SM_EVENT_CMD = 1,        // 命令事件
    SM_EVENT_LINK = 2,       // 链路事件
    SM_EVENT_NETWORK = 3,    // 网络事件
    SM_EVENT_SYSTEM = 4,     // 系统事件
};
```

### **状态机核心结构**
```c
typedef struct {
    int32_t state;                      // 当前状态
    int32_t netState;                   // 网络状态
    esp_timer_handle_t timer;           // 秒级定时器
    QueueHandle_t msgQueue;             // 消息队列
    SkSmItem smItemList[STATE_MAX];     // 子状态列表
    TaskHandle_t taskHandle;            // 状态机任务
} SkStateCtrl;
```

### **状态转换机制**
1. **事件接收**: 通过消息队列接收各模块事件
2. **事件分发**: 根据事件类型分发到对应处理函数
3. **状态切换**: 停止当前状态，启动新状态
4. **回调执行**: 调用状态相关的回调函数

## 🔗 **回调机制详解**

### **回调函数类型**

#### **1. 音频数据回调**
```c
// WebSocket音频数据回调
typedef void (*SkWsDataCallback_t)(void *arg, void *data, uint16_t len);

// 音频播放数据回调
typedef size_t (*SkFuncGetPlayData)(uint16_t *data, size_t len, SkAudioDownlinkTimeRecord *timeRecord);

// 语音识别数据回调
typedef void (*SkVcAfeDataCallback)(uint16_t *data, size_t len, uint32_t dataMode, uint32_t tickCnt);
```

#### **2. 网络事件回调**
```c
// WiFi事件回调
typedef void (*SkWifiEventCb)(uint32_t event);

// WebSocket事件回调
typedef void (*SkWsEventCallback_t)(void *arg, uint32_t event);
```

#### **3. 状态机回调**
```c
// 状态结束回调
typedef void (*SkSmStateEndCallback)(SkStateHandler handler, int32_t state);

// 语音命令回调
typedef void (*SkFuncSrCmdCallback)(int32_t commandId);
```

### **回调注册机制**
```c
// WebSocket回调注册
SkWsRegOnBinDataCallback(MyAppOnWsEvent, NULL);

// 音频播放回调注册
SkPlayerSetCallback(SkOpusDecFeedPlayAudio);

// 语音识别回调注册
SkSrSetSendFunc(SkOpusEncEnqueue);

// WiFi事件回调注册
SkWifiRegEventCb(SkSmOnWifiEvent);
```

## 🧵 **任务管理和线程架构**

### **核心任务列表**
| 任务名称 | 优先级 | 栈大小 | CPU核心 | 功能描述 |
|---------|--------|--------|---------|----------|
| SkStateMachine | 5 | 8192 | - | 状态机主控任务 |
| SkRecorderTask | 5 | 8192 | 1 | 音频录制任务 |
| SkPlayerTask | 5 | 4096 | - | 音频播放任务 |
| SkSrTask | 5 | 8192 | 1 | 语音识别任务 |
| SkSrVcTask | 5 | 8192 | 0 | 语音通信任务 |
| SkWsMainTask | 5 | 4096 | - | WebSocket通信任务 |

### **任务创建流程**
```c
// 状态机任务
xTaskCreate(SkSmMain, "SkStateMachine", 8192, &g_topStateCtrl, 5, &g_topStateCtrl.taskHandle);

// 音频任务（绑定到特定CPU核心）
xTaskCreatePinnedToCore(SkRecorderTask, "SkRecorderTask", 8192, g_afe_data, 5, &g_recordTaskHandle, 1);
xTaskCreatePinnedToCore(SkSrTask, "SkSrTask", 8192, g_afe_data, 5, &g_srTaskHandle, 1);

// WebSocket任务
xTaskCreate(SkWsMainTask, "SkWsMainTask", 4096, NULL, 5, &ctrl->rxTaskHandle);
```

## 📡 **数据流程分析**

### **音频发送流程**
```
麦克风硬件 → AudioDevRead → SkRecorderTask → SkAudioProcessFeed 
    ↓
SkSrDataIn(语音识别) + SkDataToEncode(编码处理)
    ↓
SkOpusEncEnqueue → SkOpusEncProcAudioBuf → SkRlinkFeedReordAudio
    ↓
RlinkSendAudioFrame → TCP网络传输
```

### **音频接收流程**
```
WebSocket服务器 → TCP Socket接收 → SkWsProcData协议解析
    ↓
SkWsProcFrame帧处理 → MyAppOnWsEvent回调 → MyAppOnWsAudioData数据处理
    ↓
WsAudioPacket解析 → Opus帧重组 → SkOpusDecPlayRemote解码
    ↓
SkOpusDecFeedPlayAudio播放回调 → SkPlayerTask播放任务 → SkBspPlayAudio硬件输出
```

### **语音识别流程**
```
音频输入 → AFE音频前端处理 → 唤醒词检测 → 命令识别
    ↓
SkMainCmdProc命令处理 → SkSmStateChange状态切换 → 执行相应功能
```

## 🚀 **系统初始化流程**

### **初始化顺序**
```c
void app_main(void) {
    // 1. LED初始化
    SkRledInit();
    SkRledSetEvent(SK_LED_EVENT_INIT);
    
    // 2. 硬件板级初始化
    SkBspBoardInit(16000, sizeof(uint16_t) * 8);
    
    // 3. 状态机初始化
    g_smHandler = SkSmInit();
    
    // 4. 配置管理初始化
    SkConfigInit();
    
    // 5. WiFi初始化
    SkWifiInit();
    SkWifiRegEventCb(SkSmOnWifiEvent);
    
    // 6. OTA管理初始化
    SkOtaManagerInit();
    SkOtaRegStateCallback(SkSmOtaOnStateChange);
    
    // 7. 音频编解码初始化
    SkOpusInit(16000, 1, 60);
    
    // 8. 语音识别初始化
    SkSrRegister(g_skSpeechMap, sizeof(g_skSpeechMap) / sizeof(SkSpeechMapItem), SkMainCmdProc);
    
    // 9. 网络链路初始化
    SkClinkInit(g_smHandler);
    SkRlinkInit();
    
    // 10. WebSocket初始化
    SkWsInit();
    SkWsStart();
    MyAppInit(g_smHandler);
    SkWsRegOnBinDataCallback(MyAppOnWsEvent, NULL);
    
    // 11. 音频系统初始化
    SkAudioInit(sizeof(uint16_t), 960);
    
    // 12. 回调函数设置
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    SkSrSetSendFunc(SkOpusEncEnqueue);
    SkOpusEncSetCallback(SkRlinkFeedReordAudio);
    
    // 13. 外设初始化
    SkPeripheralInit(g_smHandler);
    
    // 14. 发送初始化完成事件
    SkSmSendEvent(g_smHandler, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_INIT_OK, 0, 0);
}
```

## 🌐 **网络连接管理**

### **WiFi状态管理**
```c
enum {
    NETWORK_STATE_STA_STOP = 0,      // STA停止
    NETWORK_STATE_STA_SCANNING,      // STA扫描中
    NETWORK_STATE_STA_CONNECTING,    // STA连接中
    NETWORK_STATE_STA_CONNECTED,     // STA已连接
    NETWORK_STATE_AP_ENABLE,         // AP模式启用
};
```

### **网络事件处理流程**
```
WiFi事件 → SkStaWifiEventHandler → SkSmOnWifiEvent → 状态机事件处理
    ↓
网络状态更新 → LED状态指示 → 相应功能启动（如OTA、WebSocket连接）
```

### **WebSocket连接管理**
```c
// WebSocket主任务循环
void SkWsMainTask() {
    while (运行标志) {
        // 等待连接事件
        if (连接标志设置) {
            // 建立连接
            SkWsConnect(ctrl);
            // 进入接收循环
            SkWsRxLoop(ctrl);
            // 断开连接
            SkWsDisconnect(ctrl);
        }
    }
}
```
