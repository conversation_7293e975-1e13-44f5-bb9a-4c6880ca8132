# MP3转WAV格式转换器

这是一个用Python编写的MP3转WAV格式转换工具，提供简单版本和完整版本两种实现。

## 功能特性

### 简单版本 (mp3_to_wav_simple.py)
- 单文件转换
- 基本错误处理
- 命令行接口

### 完整版本 (mp3_to_wav_advanced.py)
- 单文件和批量转换
- 可配置音频参数（采样率、声道数、位深度）
- 递归目录搜索
- 进度显示
- 详细的转换统计
- 完善的错误处理

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install pydub tqdm
```

**注意：** pydub需要ffmpeg作为后端，请确保系统已安装ffmpeg：

- **Windows**: 下载ffmpeg并添加到PATH
- **macOS**: `brew install ffmpeg`
- **Ubuntu/Debian**: `sudo apt install ffmpeg`

## 使用方法

### 简单版本

```bash
# 转换单个文件（自动生成输出文件名）
python mp3_to_wav_simple.py music.mp3

# 指定输出文件名
python mp3_to_wav_simple.py music.mp3 output.wav
```

### 完整版本

```bash
# 转换单个文件
python mp3_to_wav_advanced.py music.mp3

# 指定输出文件
python mp3_to_wav_advanced.py music.mp3 -o output.wav

# 批量转换目录中的所有MP3文件
python mp3_to_wav_advanced.py /path/to/mp3/folder

# 递归转换子目录中的MP3文件
python mp3_to_wav_advanced.py /path/to/mp3/folder -r

# 指定输出目录
python mp3_to_wav_advanced.py /path/to/mp3/folder -o /path/to/output/folder

# 自定义音频参数
python mp3_to_wav_advanced.py music.mp3 --sample-rate 48000 --channels 1 --bit-depth 24
```

## 参数说明

### 完整版本参数
- `input`: 输入文件或目录路径
- `-o, --output`: 输出文件或目录路径
- `-r, --recursive`: 递归搜索子目录
- `--sample-rate`: 采样率（默认44100Hz）
- `--channels`: 声道数（1=单声道，2=立体声，默认2）
- `--bit-depth`: 位深度（16/24/32位，默认16）

## 示例

```bash
# 将music.mp3转换为44.1kHz立体声16位WAV
python mp3_to_wav_advanced.py music.mp3

# 批量转换并保持目录结构
python mp3_to_wav_advanced.py ./music_folder -o ./wav_folder -r

# 转换为48kHz单声道24位WAV
python mp3_to_wav_advanced.py music.mp3 --sample-rate 48000 --channels 1 --bit-depth 24
```

## 注意事项

1. 确保系统已安装ffmpeg
2. 转换大文件时可能需要较长时间
3. WAV文件通常比MP3文件大很多
4. 建议在转换前备份原始文件
