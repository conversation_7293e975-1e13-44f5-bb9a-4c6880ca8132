# sk_rlink.c 删减优化方案

## 1. 删减原则

**保持接口兼容性**: 不修改sk_rlink.h中的接口定义，确保外部模块调用不受影响
**简化内部实现**: 删除非音频流核心功能，保留最小化的音频传输能力
**保持功能完整性**: 确保音频上行和下行流程正常工作

## 2. 外部调用分析

基于代码分析，外部模块对sk_rlink的调用包括：

### 2.1 main.c中的调用
```c
SkRlinkInit();                                    // 初始化
SkRlinkSetCodedDataCallback(SkOpusDecPlayRemote, SkOpusDecGetHandler()); // 设置解码回调
```

### 2.2 sm_pm.c中的调用  
```c
SkRlinkSetPm(true);                              // 电源管理
```

### 2.3 sm_top.c中的调用
```c
SkRlinkSetFunFlag(RLINK_TASK_STOP);              // 设置任务标志
SkRlinkEventNotify(RLINK_EVENT_STOP_CALL, 0);    // 事件通知
```

## 3. 可删除的内容

### 3.1 RlinkCtrlInfo结构体字段删减

**删除统计相关字段**:
```c
// 删除这些字段，减少内存占用
uint32_t sendSuccBytes;           // 发送成功字节统计
uint32_t sendFailBytes;           // 发送失败字节统计  
uint32_t recvDataCnt;             // 接收数据计数
uint32_t rxTimeoutCnt;            // 接收超时计数
```

**删除会话管理字段**:
```c
uint16_t sessionID;               // 会话ID，音频流不需要会话管理
SkRlinkCodedDataEndCallback codedDataEndCallback; // 结束回调，简化回调机制
```

**简化错误处理字段**:
```c
uint32_t exitReason;              // 退出原因，简化错误处理
```

**保留核心字段**:
```c
uint32_t linkFlag;                // 链路状态 - 必需
char serverIp[16];                // 服务器IP - 必需
uint16_t port;                    // 端口 - 必需
int sock;                         // Socket句柄 - 必需
uint16_t sockRdy;                 // Socket状态 - 必需
uint8_t taskFlag;                 // 任务标志 - 必需
TaskHandle_t txTaskHandle;        // 发送任务 - 必需
TaskHandle_t rxTaskHandle;        // 接收任务 - 必需
QueueHandle_t msgQueue;           // 消息队列 - 必需
void *recordQueue;                // 录音队列 - 必需
uint16_t seqID;                   // 序列号 - 必需
void *decPrivate;                 // 解码私有数据 - 必需
SkRlinkCodedDataCallback codedDataCallback; // 数据回调 - 必需
```

### 3.2 可删除的函数

**统计显示函数**:
```c
void SkRlinkShowStat() {
    // 整个函数删除，改为空实现
    return;
}
```

**停止调用函数简化**:
```c
void RlinkStopCall(RlinkCtrlInfo *ctrl) {
    // 删除统计信息输出，只保留断开连接
    if (ctrl->sockRdy == RLINK_SOCK_NOT_RDY) {
        return;
    }
    RlinkDisconnect(ctrl);
    return;
}
```

**会话管理函数简化**:
```c
void SkRlinkSetCodedDataEndCallback(SkRlinkCodedDataEndCallback callback, void *private) {
    // 简化为空实现，因为删除了codedDataEndCallback字段
    return;
}
```

### 3.3 可简化的函数

**RlinkConnect()简化**:
```c
int RlinkConnect(RlinkCtrlInfo *ctrl) {
    // 删除统计字段的初始化
    // ctrl->sendSuccBytes = 0;     // 删除
    // ctrl->sendFailBytes = 0;     // 删除
    // ctrl->recvDataCnt = 0;       // 删除
    // ctrl->rxTimeoutCnt = 0;      // 删除
    
    // 保留核心连接逻辑
    // ... socket创建和连接代码保持不变
}
```

**RlinkSendRemoteMsg()简化**:
```c
int RlinkSendRemoteMsg(RlinkCtrlInfo *ctrl, void *data, size_t size) {
    int ret = send(ctrl->sock, (char *)data, size, 0);
    
    // 删除统计信息更新
    // if (ret < 0) {
    //     ctrl->sendFailBytes += size;     // 删除
    // } else {
    //     ctrl->sendSuccBytes += ret;      // 删除
    //     ctrl->sendFailBytes += (size - ret); // 删除
    // }
    
    if (ret != size) {
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}
```

**RlinkSendAudioFrame()简化时间戳**:
```c
int32_t RlinkSendAudioFrame(RlinkCtrlInfo *ctrl, DataFrame *frame, uint32_t dataLen, uint32_t timestamp) {
    int ret;
    uint16_t seq;
    SkAudioUplinkTimeRecord *timeRecord = (SkAudioUplinkTimeRecord *) frame->timeRecord;

    seq = RlinkGetTxSeqId(ctrl);
    timeRecord->seq = seq;
    timeRecord->len = dataLen;
    timeRecord->dataFlag = frame->data[1];
    timeRecord->encDoneTick = timestamp;
    timeRecord->encTxTick = SkOsGetTickCnt();
    
    // 简化时间戳记录，删除其他字段设置
    // timeRecord->relayRxTick = 0;      // 删除
    // timeRecord->relayTxTick = 0;      // 删除
    // timeRecord->decRxTick = 0;        // 删除
    // timeRecord->decStartTick = 0;     // 删除
    // timeRecord->playTick = 0;         // 删除
    
    EncodeRelayDataMsg(frame, dataLen + sizeof(frame->timeRecord));
    frame->frameHead.seqID = htons(seq);

    ret = RlinkSendRemoteMsg(ctrl, frame, dataLen + sizeof(FrameHead) + sizeof(frame->timeRecord));
    if (ret != SK_RET_SUCCESS) {
        // 简化错误处理
        static int failCnt = 0;
        failCnt++;
        if (failCnt >= 8) {
            ctrl->linkFlag = RLINK_LINK_STOP;
        }
    } 

    return ret;
}
```

**RlinkPlayAudioData()简化**:
```c
void RlinkPlayAudioData(RlinkCtrlInfo *ctrl, uint8_t *data, uint16_t payloadLen) {
    DataFrame *frame = (DataFrame *)data;
    SkAudioDownlinkTimeRecord *timeRecord = (SkAudioDownlinkTimeRecord *)frame->timeRecord;
    SkAudioDownlinkTimeRecord audioTickRecord;

    // 删除接收计数统计
    // ctrl->recvDataCnt++;              // 删除
    
    if (ctrl->codedDataCallback == NULL) {
        return;
    }
    memcpy(&audioTickRecord, timeRecord, sizeof(SkAudioDownlinkTimeRecord));
    audioTickRecord.decRxTick = SkOsGetTickCnt();
    ctrl->codedDataCallback(ctrl->decPrivate, 0,  // sessionID改为0
        data + sizeof(FrameHead) + sizeof(frame->timeRecord), 
        payloadLen - sizeof(frame->timeRecord), &audioTickRecord);

    return;
}
```

**RlinkRxLoop()简化日志和统计**:
```c
void RlinkRxLoop(RlinkCtrlInfo *ctrl) {
    // 删除详细的日志输出
    // 删除超时计数统计
    // 保留核心的数据接收和处理逻辑
    
    // 简化超时处理
    bytes = recv(sock, &buffer[rxPos], freeSize, 0);
    if (bytes <= 0) {
        // 删除详细的超时计数
        // ctrl->rxTimeoutCnt++;         // 删除
        static int timeoutCnt = 0;
        timeoutCnt++;
        if (timeoutCnt >= RLINK_TIMEOUT_COUNT) {
            break;
        }
        continue;
    } else {
        // ctrl->rxTimeoutCnt = 0;       // 删除
        timeoutCnt = 0;  // 使用局部变量
    }
    
    // 删除详细的调试日志
    // ESP_LOGI(TAG, "RlinkRxLoop: move...");  // 删除这类日志
}
```

**RlinkProcLocalMsg()简化事件处理**:
```c
int RlinkProcLocalMsg(RlinkCtrlInfo *ctrl, TickType_t ticks) {
    int ret = SK_RET_SUCCESS;
    RlinkMsg msg;

    if (xQueueReceive(ctrl->msgQueue, &msg, ticks) != pdPASS) {
        return ret;
    }
    
    // 简化事件处理，删除详细日志
    switch (msg.event) {
        case RLINK_EVENT_STOP_CALL:
            ctrl->linkFlag = RLINK_LINK_STOP;
            break;

        case RLINK_EVENT_TX_DATA:
            RlinkSendAudioData(ctrl, (SkAudioBuf *)msg.arg, msg.timestamp);
            break;
            
        case RLINK_EVENT_RX_EXIT:
            // 简化处理，删除详细日志
            RlinkStopCall(ctrl);
            break;

        default:
            // 删除未知事件的日志输出
            break;
    }

    return ret;
}
```

## 4. 保持兼容性的接口实现

### 4.1 电源管理接口保持不变
```c
void SkRlinkSetPm(bool flag) {
    // 保持原有逻辑，确保sm_pm.c调用正常
    if (flag) {
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
        g_rlinkCtrl.taskFlag = RLINK_TASK_STOP;
#endif
    } else {
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
        SkRlinkStartTasks();
#endif
    }
}
```

### 4.2 任务标志接口保持不变
```c
void SkRlinkSetFunFlag(uint8_t flag) {
    // 保持原有逻辑，确保sm_top.c调用正常
    g_rlinkCtrl.taskFlag = flag;
}
```

### 4.3 事件通知接口保持不变
```c
int SkRlinkEventNotify(uint8_t event, uint16_t param1) {
    // 保持原有逻辑，确保外部事件通知正常
    RlinkMsg msg;
    if (g_rlinkCtrl.msgQueue == NULL) {
        return SK_RET_FAIL;
    }
    msg.event = event;
    msg.param1 = param1;
    msg.arg = NULL;
    xQueueSend(g_rlinkCtrl.msgQueue, &msg, portMAX_DELAY);
    return SK_RET_SUCCESS;
}
```

## 5. 删减效果预估

### 5.1 代码量减少
- **总行数**: 从622行减少到约450行 (减少28%)
- **结构体大小**: RlinkCtrlInfo减少约40字节
- **函数简化**: 10个函数得到简化，删除大量调试和统计代码

### 5.2 功能保持
✅ **音频上行流程**: 完全保持  
✅ **音频下行流程**: 完全保持  
✅ **TCP连接管理**: 完全保持  
✅ **任务管理**: 完全保持  
✅ **外部接口兼容性**: 完全保持  

### 5.3 删除的功能
❌ **详细统计信息**: 发送/接收字节统计、超时计数等  
❌ **复杂错误处理**: 详细的错误原因记录和日志  
❌ **会话管理**: sessionID相关功能  
❌ **详细时间戳**: 只保留基本的编码和接收时间戳  
❌ **调试日志**: 大量的ESP_LOGI/ESP_LOGD输出  

## 6. 实施建议

1. **分步实施**: 先删除统计相关代码，再简化错误处理，最后删除详细日志
2. **保持测试**: 每次删减后确保音频流功能正常
3. **接口兼容**: 确保所有外部调用的接口行为保持一致
4. **性能验证**: 验证删减后的内存占用和CPU使用率改善

这个方案专注于sk_rlink内部的简化，不影响其他模块，是一个安全且有效的删减策略。
