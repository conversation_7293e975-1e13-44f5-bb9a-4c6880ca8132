/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: my_app.h
 * @description: WebSocket音频处理模块接口
 * @author: <PERSON>
 * @date: 2025-07-30
 */
#ifndef MY_APP_H
#define MY_APP_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

// WebSocket音频包结构（极简定义）
typedef struct __attribute__((packed)) {
    uint8_t version;        // 0x01
    uint8_t type;           // 0x01 (音频类型)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // Opus数据长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} WsAudioPacket;

/**
 * @brief 启动WebSocket连接
 * @note 在WiFi连接成功后调用
 */
void MyAppStartWebSocketConnection(void);

/**
 * @brief WebSocket音频数据回调函数
 * @param arg 回调参数（未使用）
 * @param data 音频数据指针
 * @param len 数据长度
 */
void MyAppOnWsAudioData(void *arg, void *data, uint16_t len);

/**
 * @brief 获取WebSocket统计信息
 * @param success 成功处理的帧数（可为NULL）
 * @param error 错误处理的帧数（可为NULL）
 */
void MyAppGetWebSocketStats(uint32_t *success, uint32_t *error);

/**
 * @brief 重置WebSocket统计信息
 */
void MyAppResetWebSocketStats(void);

/**
 * @brief 检查WebSocket连接状态
 * @return true 已连接，false 未连接
 */
bool MyAppIsWebSocketConnected(void);

/**
 * @brief 设置WebSocket连接状态
 * @param connected 连接状态
 */
void MyAppSetWebSocketConnected(bool connected);

/**
 * @brief WebSocket事件回调函数（对外接口）
 * @param arg 回调参数
 * @param data 数据指针
 * @param len 数据长度
 */
void MyAppOnWsEvent(void *arg, void *data, uint16_t len);

/**
 * @brief 主命令处理函数
 * @param cmd 命令ID
 * @param smHandler 状态机句柄
 */
void MyAppMainCmdProc(int32_t cmd, void *smHandler);

/**
 * @brief 初始化my_app模块
 * @param smHandler 状态机句柄
 */
void MyAppInit(void *smHandler);

#ifdef __cplusplus
}
#endif

#endif // MY_APP_H
