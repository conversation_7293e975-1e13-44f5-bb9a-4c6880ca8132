/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_websocket.c
 * @description: Websocket协议实现, 实现基于TCP协议的Websocket, 支持协议数据加密
 * @author: <PERSON>
 * @date: 2025-07-17
 */
#include <string.h>
#include <stdlib.h>
#include <freertos/FreeRTOS.h>
#include <sys/socket.h>
#include "sk_log.h"
#include "sk_os.h"
#include "sk_common.h"
#include "sk_websocket.h"

static const char *TAG = "SkWs";

#define SK_WS_BUFF_SIZE 1280
#define SK_WS_CTRL_FRAME_MAX_SIZE       32
#define MASK_DATA_LEN                   4
#define SK_WS_FRAME_HEADER_LEN          2
#define SK_WS_FRAME_HEADER_EXT_LEN      4

#define SK_WS_EVENT_CONNECT_BIT         (0x01)
#define SK_WS_EVENT_DISCONNECT_BIT      (0x02)
#define SK_WS_EVENT_STOP_BIT            (0x04)
#define SK_WS_HS_WAIT_CNT               (100)

typedef struct {
    bool connected;
    uint16_t port;
    int sock;
    EventGroupHandle_t eventGroup;
    TaskHandle_t rxTaskHandle;
    uint8_t *buffer;

    char serverIp[16];
    void *onTextFrameArg;
    void *onBinaryFrameArg;
    void *onEventArg;
    SkWsDataCallback_t onTextFrameCb;
    SkWsDataCallback_t onBinaryFrameCb;
    SkWsEventCallback_t onEventCb;
} SkWebsocket_t;


void SkWsMainTask();
int32_t SkWsConnect(SkWebsocket_t *ctrl);
void SkWsDisconnect(SkWebsocket_t *ctrl);
int SkWsLowConnect(SkWebsocket_t *ctrl);
int32_t SkWsSendHandshakeReq(SkWebsocket_t *ctrl);
static int32_t SkWsGetHandshakeState(const char *buffer);
int32_t SkWsRecvHandshakeRsp(SkWebsocket_t *ctrl);
int32_t SkWsHandshake(SkWebsocket_t *ctrl);
int32_t SkWsGetPayload(uint8_t *buffer, int dataLen, uint8_t* opcode, uint8_t **payloadPos, uint32_t *payloadLen);
int32_t SkWsProcFrame(SkWebsocket_t *ctrl, uint8_t opcode, uint8_t *data, int dataLen);
int32_t SkWsProcData(SkWebsocket_t *ctrl, uint8_t *buffer, int dataLen);
int32_t SkWsSendCtrlFrame(SkWebsocket_t *ctrl, uint8_t opcode, const void* data, size_t len);

SkWebsocket_t g_websocketCtrl;

int32_t SkWsInit() {
    SkWebsocket_t *websocket = &g_websocketCtrl;

    websocket->eventGroup = xEventGroupCreate();
    if (websocket->eventGroup == NULL) {
        SK_LOGI(TAG, "websocket->eventGroup create fail!");
        return SK_RET_FAIL;
    }
    websocket->connected = false;
    websocket->sock = -1;
    if (websocket->buffer == NULL) {
        websocket->buffer = (uint8_t *)malloc(SK_WS_BUFF_SIZE);
        if (websocket->buffer == NULL) {
            SK_LOGI(TAG, "websocket->buffer malloc fail!");
            return SK_RET_FAIL;
        }
    }
    
    return SK_RET_SUCCESS;
}

void SkWsDeinit() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    if (ctrl->buffer != NULL) {
        free(ctrl->buffer);
        ctrl->buffer = NULL;
    }
    if (ctrl->eventGroup != NULL) {
        vEventGroupDelete(ctrl->eventGroup);
        ctrl->eventGroup = NULL;
    }
    memset(ctrl, 0, sizeof(SkWebsocket_t));
    return;
}

void SkWsRegOnBinDataCallback(SkWsDataCallback_t callback, void *arg) {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    ctrl->onBinaryFrameCb = callback;
    ctrl->onBinaryFrameArg = arg;
    return;
}

void SkWsRegOnTxtDataCallback(SkWsDataCallback_t callback, void *arg) {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    ctrl->onTextFrameCb = callback;
    ctrl->onTextFrameArg = arg;
    return;
}

void SkWsRegOnEventCallback(SkWsEventCallback_t callback, void *arg) {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    ctrl->onEventCb = callback;
    ctrl->onEventArg = arg;
    return;
}

void SkWsSetServerIp(const char *ip, uint16_t port) {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    strncpy(ctrl->serverIp, ip, sizeof(ctrl->serverIp));
    ctrl->port = port;
    return;
}

void SkWsStart() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    xEventGroupClearBits(g_websocketCtrl.eventGroup, SK_WS_EVENT_STOP_BIT);
    xTaskCreate(SkWsMainTask, "SkWsMainTask", 4096, NULL, 5, &ctrl->rxTaskHandle);
    return;
}

void SkWsStop() {
    xEventGroupSetBits(g_websocketCtrl.eventGroup, SK_WS_EVENT_STOP_BIT);
    return;
}

void SkWsStartConnect() {
    xEventGroupClearBits(g_websocketCtrl.eventGroup, SK_WS_EVENT_DISCONNECT_BIT);
    xEventGroupSetBits(g_websocketCtrl.eventGroup, SK_WS_EVENT_CONNECT_BIT);
    return;
}

void SkWsStopConnect() {
    xEventGroupClearBits(g_websocketCtrl.eventGroup, SK_WS_EVENT_CONNECT_BIT);
    xEventGroupSetBits(g_websocketCtrl.eventGroup, SK_WS_EVENT_DISCONNECT_BIT);
    return;
}

bool SkWsIsConnected() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    return ctrl->connected;
}

void SkWsMainTask() {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    uint32_t flagWaitConnect = SK_WS_EVENT_CONNECT_BIT | SK_WS_EVENT_STOP_BIT;
    uint32_t flagAtReconnect = SK_WS_EVENT_DISCONNECT_BIT | SK_WS_EVENT_STOP_BIT;
    uint32_t event = 0;

    while ((event & SK_WS_EVENT_STOP_BIT) == 0) {
        event = xEventGroupGetBits(ctrl->eventGroup);
        if ((event & SK_WS_EVENT_CONNECT_BIT) == 0) {
            event = xEventGroupWaitBits(ctrl->eventGroup, flagWaitConnect, pdFALSE, pdFALSE, pdMS_TO_TICKS(5000));
            continue;
        }
        if (SkWsConnect(ctrl) != SK_RET_SUCCESS) {
            event = xEventGroupWaitBits(ctrl->eventGroup, flagAtReconnect, pdFALSE, pdFALSE, pdMS_TO_TICKS(5000));
            continue;
        }
        if (ctrl->onEventCb != NULL) {
            ctrl->onEventCb(ctrl->onEventArg, SK_WS_EVENT_CONNECTED);
        }

        SkWsDisconnect(ctrl);
        if (ctrl->onEventCb != NULL) {
            ctrl->onEventCb(ctrl->onEventArg, SK_WS_EVENT_DISCONNECTED);
        }
    }
    SK_LOGI(TAG, "Websocket task exit");
    vTaskDelete(NULL);
}

#ifdef WS_SUPPORT_SK_BUF_POOL
int32_t SkWsSend(SkBufPoolNode *node, bool binary) {
    SkWebsocket_t *ctrl = &g_websocketCtrl;
    size_t len = node->tail - node->head;
    size_t totalLen;

    if (ctrl->connected == false) {
        SK_LOGE(TAG, "Not connected");
        return SK_RET_FAIL;
    }
    if (len == 0) {
        return SK_RET_FAIL;
    }

    uint8_t flag = binary ? SK_WS_PACKET_FLAG_BINARY : SK_WS_PACKET_FLAG_TEXT;
    totalLen = SkWsPacketData(node->data, node->size, node->data + node->head, len, flag);
    if (totalLen == 0) {
        return SK_RET_FAIL;
    }

    if (SkWsSendRaw(node->data, totalLen) != SK_RET_SUCCESS) {
        SK_LOGE(TAG, "send fail!");
        return SK_RET_FAIL;
    }
    
    return SK_RET_SUCCESS;
}
#endif

size_t SkWsPacketData(uint8_t *out, size_t outLen, uint8_t *in, size_t inLen, uint8_t flag) {
    uint8_t headerByte, lengthByte;
    int32_t writePos, headerSize;
    uint8_t *payload = NULL;
    bool binary = (flag & SK_WS_PACKET_FLAG_BINARY) != 0;

    // 2字节帧头 + (2字节长度 +) 4字节mask, 超过126字节需要扩展长度
    if (inLen < 126) {        
        headerSize = SK_WS_FRAME_HEADER_LEN + MASK_DATA_LEN;
        lengthByte = inLen | 0x80;
    } else if (inLen < 65536) {
        headerSize = SK_WS_FRAME_HEADER_EXT_LEN + MASK_DATA_LEN;
        lengthByte = 126 | 0x80;
    } else {
        SK_LOGE(TAG, "Payload length exceeds 65535 bytes not support");
        return 0;
    }

    if (outLen < headerSize + inLen) {
        SK_LOGE(TAG, "Out buffer size not enough");
        return 0;
    }

    // 第一个字节：FIN 位 + 操作码. FIN 位固定设置, 不支持分片
    headerByte = binary ? SK_WS_FRAME_TYPE_BINARY : SK_WS_FRAME_TYPE_TEXT;
    headerByte |= 0x80; 
    out[0] = headerByte;
    // 第二个字节：MASK 位 + 有效载荷长度, 固定使用MASK
    out[1] = lengthByte;
    writePos = 2;
    // 超过126字节需要扩展长度
    if (inLen >= 126) {
        out[writePos] = (inLen >> 8) & 0xFF;
        writePos++;
        out[writePos] = inLen & 0xFF;
        writePos++;
    }

    // 生成随机的4字节mask
    uint8_t mask[4];
    for (int i = 0; i < 4; ++i) {
        mask[i] = rand() & 0xFF;
        out[writePos] = mask[i];
        writePos++;
    }

    // 添加并mask处理有效载荷
    payload = out + headerSize;
    for (size_t i = 0; i < inLen; ++i) {
        payload[i] = in[i] ^ mask[i % 4];
    }

    return headerSize + inLen;
}

int32_t SkWsSendRaw(uint8_t *data, size_t len) {
    if (g_websocketCtrl.connected == false) {
        SK_LOGE(TAG, "Not connected");
        return SK_RET_FAIL;
    }
    if (send(g_websocketCtrl.sock, data, len, 0) != len) {
        SK_LOGE(TAG, "send fail!");
        return SK_RET_FAIL;
    }
    return SK_RET_SUCCESS;
}

int32_t SkWsConnect(SkWebsocket_t *ctrl) {
    if (SkWsLowConnect(ctrl) != SK_RET_SUCCESS) {
        SK_LOGI(TAG, "SkWsLowConnect fail!");
        return SK_RET_FAIL;
    }

    if (SkWsHandshake(ctrl) != SK_RET_SUCCESS) {
        SK_LOGI(TAG, "SkWsHandshake fail!");
        return SK_RET_FAIL;
    }
    ctrl->connected = true;

    return SK_RET_SUCCESS;
}

void SkWsDisconnect(SkWebsocket_t *ctrl) {
    SK_LOGI(TAG, "Start disconnect");
    if (ctrl->connected) {
        SkWsSendCtrlFrame(ctrl, SK_WS_FRAME_TYPE_CLOSE, NULL, 0);
    }
    ctrl->connected = false;
    if (ctrl->sock != -1) {
        close(ctrl->sock);
        ctrl->sock = -1;
    }
    return;
}

int SkWsLowConnect(SkWebsocket_t *ctrl) {
	int ret, sock;
	uint32_t addr;
    struct sockaddr_in sockAddr;

    memset(&sockAddr, 0, sizeof(struct sockaddr));
    sockAddr.sin_family = AF_INET;
    sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);

	addr = inet_addr(ctrl->serverIp);
	memcpy((char *)&sockAddr.sin_addr, (char *)&addr, sizeof(addr));
    sockAddr.sin_port = htons(ctrl->port);

    // 设置 send 超时时间（单位：毫秒）
    struct timeval sendTimeout = {0, 100000};
    if (setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &sendTimeout, sizeof(sendTimeout)) < 0) {
        perror("Failed to set SO_SNDTIMEO");
        close(sock);
        return -1;
    }

    // 设置 recv 超时时间（单位：毫秒）
    struct timeval recvTimeout = {0, 100000};
    if (setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &recvTimeout, sizeof(recvTimeout)) < 0) {
        perror("Failed to set SO_RCVTIMEO");
        close(sock);
        return -1;
    }

	ret = connect(sock, (struct sockaddr *)&sockAddr, sizeof(struct sockaddr));
    if (ret != 0) {
        SK_LOGI(TAG, "connect ack failed! socket num=%d", sock);
        closesocket(sock);
        return SK_RET_FAIL;
    }
	ctrl->sock = sock;

    return SK_RET_SUCCESS;
}

int32_t SkWsSendHandshakeReq(SkWebsocket_t *ctrl) {
    char secKey[25];
    char *buffer = (char *)ctrl->buffer;

    SkOsGenerateWsKey(secKey, sizeof(secKey));
    strcpy(buffer, "GET / HTTP/1.1\r\n");
    strcat(buffer, "Host: ");
    strcat(buffer, ctrl->serverIp);
    strcat(buffer, "\r\n");
    strcat(buffer, "Connection: Upgrade\r\n");
    strcat(buffer, "Upgrade: websocket\r\n");
    strcat(buffer, "Sec-WebSocket-Version: 13\r\n");
    strcat(buffer, "Sec-WebSocket-Key: ");
    strcat(buffer, secKey);
    strcat(buffer, "\r\n\r\n");

    if (send(ctrl->sock, ctrl->buffer, strlen((char *)ctrl->buffer), 0) < 0) {
        SK_LOGI(TAG, "send handshake fail!");
        return SK_RET_FAIL;
    }

    return SK_RET_SUCCESS;
}

static int32_t SkWsGetHandshakeState(const char *buffer) {
    const char http[] = "HTTP/";
    const char *found = strcasestr(buffer, http);
    char statusCode[4];

    if (found == NULL) {
        return -1;
    }

    found += sizeof(http) - 1;
    found = strchr(found, ' ');
    if (found == NULL) {
        return -1;
    }

    found++;
    strncpy(statusCode, found, 3);
    statusCode[3] = '\0';

    int32_t code = atoi(statusCode);
    ESP_LOGD(TAG, "HTTP status code is %d", code);

    return code == 0 ? -1 : code;
}

int32_t SkWsRecvHandshakeRsp(SkWebsocket_t *ctrl) {
    char *delimiter = NULL;
    int32_t bytes;
    size_t rxPos = 0;
    size_t freeSize = SK_WS_BUFF_SIZE;
    uint32_t waitCnt = 0;

    // Read byte by byte until \r\n\r\n
    memset(ctrl->buffer, 0, SK_WS_BUFF_SIZE);
    while (waitCnt < SK_WS_HS_WAIT_CNT) {
        bytes = recv(ctrl->sock, &ctrl->buffer[rxPos], freeSize, 0);
        if (bytes <= 0) {
            waitCnt++;
            continue;
        }

        rxPos += bytes;
        freeSize -= bytes;
        if (rxPos >= SK_WS_BUFF_SIZE) {
            // 缓冲区已满，还未匹配成功, 连接失败.
            break;
        }
        ctrl->buffer[rxPos] = '\0';
        delimiter = strstr((char *)ctrl->buffer, "\r\n\r\n");
        SK_LOGD(TAG, "recv: %s", ctrl->buffer);
        if (delimiter != NULL) {
            // 这里只检查了状态码，后续根据需求检查其他字段
            if (SkWsGetHandshakeState((char *)ctrl->buffer) == 101) {
                // 匹配成功，当前所有数据清零, 都不予保留.
                return SK_RET_SUCCESS;
            } else {
                SK_LOGE(TAG, "SkWsGetHandshakeState fail!");
                return SK_RET_FAIL;
            }
        }
    }

    return SK_RET_FAIL;
}

int32_t SkWsHandshake(SkWebsocket_t *ctrl) {
    if (SkWsSendHandshakeReq(ctrl) != SK_RET_SUCCESS) {
        return SK_RET_FAIL;
    }
    
    if (SkWsRecvHandshakeRsp(ctrl) != SK_RET_SUCCESS) {
        return SK_RET_FAIL;
    }

    return SK_RET_SUCCESS;
}

/**
 * @brief 获取帧数据
 * @param ctrl WebSocket控制结构体
 * @param buffer 数据缓冲区
 * @param dataLen 数据长度
 * @param opcode 帧类型
 * @param payloadPos 有效载荷位置
 * @param payloadLen 有效载荷长度
 * @return int32_t 处理结果，0: 数据不够, -1: 处理失败, >0: 总长度
 */
int32_t SkWsGetPayload(uint8_t *buffer, int dataLen, uint8_t* opcode, uint8_t **payloadPos, uint32_t *payloadLen) {
    bool fin = (buffer[0] & 0x80) != 0;
    uint8_t mask = buffer[1] & 0x80;
    uint32_t len = buffer[1] & 0x7F;
    size_t headerLen, packetLen;

    if (!fin) {
        SK_LOGE(TAG, "Fragmented frames are not supported");
        return -1;
    }

    if (len == 126) {
        // 长度超过126, 扩展了2字节长度
        if (dataLen < SK_WS_FRAME_HEADER_EXT_LEN) {
            return 0;
        }
        len = (buffer[2] << 8) | buffer[3];
        headerLen = SK_WS_FRAME_HEADER_EXT_LEN;
    } else if (len == 127) {
        // 长度超过65535, 扩展了8字节长度, 不予支持
        SK_LOGE(TAG, "Length > 65535 is not supported");
        return -1;
    } else {
        // 长度小于126, 不扩展长度
        headerLen = SK_WS_FRAME_HEADER_LEN;
    }
    if (mask != 0) {
        headerLen += MASK_DATA_LEN;
    }

    packetLen = headerLen + len;
    if (packetLen >= SK_WS_BUFF_SIZE) {
        SK_LOGE(TAG, "Payload length exceeds buffer size");
        return -1;
    }
    if (packetLen > dataLen) {
        return 0;
    }

    *opcode = buffer[0] & 0x0F;
    *payloadPos = &buffer[headerLen];
    *payloadLen = len;

    if (mask != 0) {
        uint8_t maskKey[4];
        memcpy(maskKey, &buffer[headerLen - MASK_DATA_LEN], MASK_DATA_LEN);

        // 解码有效载荷
        uint8_t *payload = &buffer[headerLen];
        for (size_t i = 0; i < len; ++i) {
            payload[i] ^= maskKey[i % MASK_DATA_LEN];
        }
    }
    return headerLen + len;
}

int32_t SkWsSendCtrlFrame(SkWebsocket_t *ctrl, uint8_t opcode, const void* data, size_t len) {
    size_t headerLen;
    uint8_t frame[SK_WS_CTRL_FRAME_MAX_SIZE];

    if (len > (SK_WS_CTRL_FRAME_MAX_SIZE - SK_WS_FRAME_HEADER_LEN - MASK_DATA_LEN)) {
        SK_LOGE(TAG, "Ctrl Frame payload length %d exceeds", len);
        return SK_RET_INVALID_PARAM;
    }

    headerLen = SK_WS_FRAME_HEADER_LEN;
    frame[0] = 0x80 | opcode;           // 第一个字节：FIN 位 + 操作码
    frame[1] = 0x80 | len;              // 第二个字节：MASK 位 + 有效载荷长度

    // 生成随机的4字节掩码
    uint8_t mask[MASK_DATA_LEN];
    for (int i = 0; i < MASK_DATA_LEN; ++i) {
        mask[i] = rand() & 0xFF;
        frame[headerLen + i] = mask[i];
    }
    headerLen += MASK_DATA_LEN;

    // 添加并掩码处理有效载荷
    const uint8_t* payload = (const uint8_t*)data;
    for (size_t i = 0; i < len; ++i) {
        frame[headerLen + i] = payload[i] ^ mask[i % MASK_DATA_LEN];
    }

    // 发送帧
    return send(ctrl->sock, frame, len + headerLen, 0);
}

/**
 * @brief 处理
 * @param ctrl WebSocket控制结构体
 * @param data 帧数据
 * @param dataLen 帧长度
 * @return int32_t 处理结果, SkRetDef_e
 */
int32_t SkWsProcFrame(SkWebsocket_t *ctrl, uint8_t opcode, uint8_t *data, int dataLen) {
    switch (opcode) {
        case SK_WS_FRAME_TYPE_TEXT:
            if (ctrl->onTextFrameCb) {
                ctrl->onTextFrameCb(ctrl->onTextFrameArg, data, dataLen);
            }
            return SK_RET_SUCCESS;
        case SK_WS_FRAME_TYPE_BINARY:
            if (ctrl->onBinaryFrameCb) {
                ctrl->onBinaryFrameCb(ctrl->onBinaryFrameArg, data, dataLen);
            }
            return SK_RET_SUCCESS;
        case SK_WS_FRAME_TYPE_CLOSE:
            SK_LOGE(TAG, "Close frame received");
            ctrl->connected = false;
            return SK_RET_FAIL;
        case SK_WS_FRAME_TYPE_PING:
            SkWsSendCtrlFrame(ctrl, SK_WS_FRAME_TYPE_PONG, data, dataLen);    
            if (ctrl->onEventCb != NULL) {
                ctrl->onEventCb(ctrl->onEventArg, SK_WS_EVENT_PINGPONG);
            }
            return SK_RET_SUCCESS;
        case SK_WS_FRAME_TYPE_PONG:
            return SK_RET_SUCCESS;
        case 0x0:   // 延续帧, 当前不分片, 直接归类到default
        default:
            SK_LOGE(TAG, "Opcode %d not supportted.", opcode);
            return SK_RET_FAIL;
    }
}

int32_t SkWsProcData(SkWebsocket_t *ctrl, uint8_t *buffer, int dataLen) {
    uint8_t opcode;
    int32_t ret, procLen, lastLen;
    uint32_t payloadLen;
    uint8_t *payload = NULL;

    ret = 0;
    procLen = 0;
    lastLen = dataLen;
    while (lastLen >= SK_WS_FRAME_HEADER_LEN) {
        ret = SkWsGetPayload(&buffer[procLen], lastLen, &opcode, &payload, &payloadLen);
        // 小于0发生错误需要断开连接
        if (ret < 0) {
            break;
        }
        lastLen -= ret;
        procLen += ret;

        SkWsProcFrame(ctrl, opcode, payload, payloadLen);
        ESP_LOGD(TAG, "Total=%d, procLen=%d, lastLen=%d", dataLen, procLen, lastLen);
    }
    if (ret == -1) {
        SK_LOGE(TAG, "procLen=%d, lastLen=%d, dataLen=%d, %02x %02x", 
            procLen, lastLen, dataLen, buffer[procLen], buffer[procLen + 1]);
        return -1;
    }

    return procLen;
}
