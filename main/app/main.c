/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: main.c
 * @description: 程序入口
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdlib.h>
#include <stdio.h>
#include "sdkconfig.h"
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#include <netdb.h>            // struct addrinfo
#include <arpa/inet.h>
#include "sk_os.h"
#include "sk_board.h"
#include "sk_audio.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_sm.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_dfx.h"
#include "sk_log.h"
#include "sk_websocket.h"
#include "sk_test.h"
#include "sk_ota_api.h"

#define RESERVE_MEM_SIZE_PER_BLOCK (2048)

// WebSocket音频包结构（极简定义）
typedef struct __attribute__((packed)) {
    uint8_t version;        // 0x01
    uint8_t type;           // 0x01 (音频类型)
    uint16_t seqNum;        // 序列号
    uint16_t payloadLen;    // Opus数据长度
    uint16_t resv;          // 保留字段
    uint8_t data[0];        // Opus音频数据
} WsAudioPacket;

static const char *TAG = "SmartKid";

SkSpeechMapItem g_skSpeechMap[] = {
    {SPEECH_CMD_EVENT_CHAT, "wu kong"},
    {SPEECH_CMD_EVENT_MUSIC, "yin yue"},
    {SPEECH_CMD_EVENT_CONFIG, "pei zhi"},
    {SPEECH_CMD_EVENT_CONFIG, "she zhi"},
    {SPEECH_CMD_EVENT_QUERY, "cha cha"},
    {SPEECH_CMD_EVENT_VOLUP, "sheng ying da yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao gao ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "tiao da ying liang"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLUP, "da sheng dian"},
    {SPEECH_CMD_EVENT_VOLUP, "zai da yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "sheng ying xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao di ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "tiao xiao ying liang"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng yi dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "xiao sheng dian"},
    {SPEECH_CMD_EVENT_VOLDOWN, "zai xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da ying liang"},
    {SPEECH_CMD_EVENT_VOLMAX, "zui da sheng"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao yi dian"},
    {SPEECH_CMD_EVENT_VOLMIN, "zui xiao sheng"},
    {SPEECH_CMD_EVENT_HELP, "qiu zhu"},
    {SPEECH_CMD_EVENT_PAUSE, "zan ting"},
    {SPEECH_CMD_EVENT_CONFIRM, "que ding"},
    {SPEECH_CMD_EVENT_QUIT, "tui chu"},
    {SPEECH_CMD_EVENT_QUIT, "ting zhi"},
    {SPEECH_CMD_EVENT_PREV, "shang yi ge"},
    {SPEECH_CMD_EVENT_NEXT, "xia yi ge"},
    {SPEECH_CMD_EVENT_RESUME, "ji xu"},
    {SPEECH_CMD_EVENT_QUIT, "qu xiao"},
    {SPEECH_CMD_EVENT_INFO, "zhuang tai"},
    {SPEECH_CMD_EVENT_START_DBG, "qi dong"},
    {SPEECH_CMD_EVENT_STOP_DBG, "duan kai"},
    {SPEECH_CMD_EVENT_SLEEP, "dai ji"},
    {SPEECH_CMD_EVENT_CALL, "fu jiao"},
    {SPEECH_CMD_EVENT_CALL, "hu jiao"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu ying"},
    {SPEECH_CMD_EVENT_MIC_ON, "lu yin"},
    {SPEECH_CMD_EVENT_MIC_OFF, "guan bi"},
    {SPEECH_CMD_EVENT_CALL, "qi dong hu jiao"},
    {SPEECH_CMD_EVENT_CALL, "da kai dian hua"},
    {SPEECH_CMD_EVENT_CONFIG, "pei wang"},
};

SkStateHandler g_smHandler;

// WebSocket连接启动函数（在WiFi连接成功后调用）
void StartWebSocketConnection(void) {
    static bool ws_connected = false;

    if (!ws_connected) {
        SkWsStartConnect();
        ws_connected = true;
        SK_LOGI(TAG, "WebSocket connection started after WiFi ready");
        SkOpusDecUnmuteRemote();
    }
}

// WebSocket音频数据回调 - 支持真正的Opus解码
void OnWsAudioData(void *arg, void *data, uint16_t len) {
    WsAudioPacket *pkt;
    SkAudioDownlinkTimeRecord timeRecord = {0};
    const uint16_t headerSize = 8;  // 固定包头大小：1+1+2+2+2 = 8字节
    static uint32_t success_count = 0;
    static uint32_t error_count = 0;

    // 检查数据指针和最小长度
    if (data == NULL || len < headerSize) {
        SK_LOGE("WsAudio", "Invalid data: data=%p, len=%d, min_len=%d", data, len, headerSize);
        error_count++;
        return;
    }

    pkt = (WsAudioPacket *)data;

    // 验证数据格式
    if (pkt->version != 1 || pkt->type != 1) {
        SK_LOGE("WsAudio", "Invalid packet format: ver=%d, type=%d", pkt->version, pkt->type);
        error_count++;
        return;
    }

    // 验证负载长度（实际数据长度 = 总长度 - 包头长度）
    uint16_t actualPayloadLen = len - headerSize;
    if (pkt->payloadLen != actualPayloadLen) {
        SK_LOGE("WsAudio", "Payload length mismatch: expected=%d, actual=%d",
                pkt->payloadLen, actualPayloadLen);
        error_count++;
        return;
    }

    // 检查负载长度是否合理（Opus帧通常在20-200字节之间）
    if (pkt->payloadLen == 0 || pkt->payloadLen > 1024) {
        SK_LOGE("WsAudio", "Invalid payload length: %d", pkt->payloadLen);
        error_count++;
        return;
    }

    // 构造时间戳记录
    timeRecord.decRxTick = SkOsGetTickCnt();

    // 检测数据类型并相应处理
    if (pkt->payloadLen >= 2 && pkt->data[0] == 0xFC && pkt->data[1] == 0x00) {
        // 处理伪Opus数据（向后兼容）
        SK_LOGW("WsAudio", "Received pseudo-Opus data (deprecated): seq=%d, len=%d",
                pkt->seqNum, pkt->payloadLen);
        error_count++;
        return; // 不再支持伪Opus数据
    } else {
        
        // 使用栈缓冲区（Opus帧通常不超过200字节，加上4字节头部）
        uint16_t total_frame_size = pkt->payloadLen + 4;

        if (total_frame_size > 1028) { // 1024 + 4字节的安全检查
            SK_LOGE("WsAudio", "Opus frame too large: %d bytes", total_frame_size);
            error_count++;
            return;
        }

        uint8_t opus_frame[1028]; // 栈缓冲区，避免malloc/free开销

        // 安全地构建完整的Opus帧
        // 前4字节：seqNum(2字节) + payloadLen(2字节)
        opus_frame[0] = pkt->seqNum & 0xFF;           // seqNum低字节
        opus_frame[1] = (pkt->seqNum >> 8) & 0xFF;    // seqNum高字节
        opus_frame[2] = pkt->payloadLen & 0xFF;       // payloadLen低字节
        opus_frame[3] = (pkt->payloadLen >> 8) & 0xFF; // payloadLen高字节

        // 复制Opus数据
        memcpy(opus_frame + 4, pkt->data, pkt->payloadLen);

        // 调用解码器
        int32_t ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 0xFFFF,
                                         opus_frame, total_frame_size, &timeRecord);

        if (ret == SK_RET_SUCCESS) {
            success_count++;
            // 每100帧打印一次统计信息
            if (success_count % 100 == 0) {
                SK_LOGI("WsAudio", "Opus decode stats: success=%u, error=%u, seq=%d, len=%d",
                        success_count, error_count, pkt->seqNum, pkt->payloadLen);

                // 显示帧头信息（调试用）
                SK_LOGD("WsAudio", "Frame header: [%02X %02X %02X %02X] + %d bytes opus data",
                        opus_frame[0], opus_frame[1], opus_frame[2], opus_frame[3], pkt->payloadLen);
            }
        } else {
            error_count++;
            SK_LOGE("WsAudio", "Opus decode failed: seq=%d, len=%d, ret=%d",
                    pkt->seqNum, pkt->payloadLen, ret);
            SK_LOGE("WsAudio", "Frame header: [%02X %02X %02X %02X], opus_data: [%02X %02X %02X %02X]",
                    opus_frame[0], opus_frame[1], opus_frame[2], opus_frame[3],
                    opus_frame[4], opus_frame[5], opus_frame[6], opus_frame[7]);
        }

    }
}

void SkMainCmdProc(int32_t cmd) {
    if (g_smHandler == NULL) {
        return;
    }

    // 处理特殊命令（可选：保留手动重连功能）
    switch (cmd) {
        case SPEECH_CMD_EVENT_CONFIG:  // "配置" 或 "设置" 命令可重新连接WebSocket
            StartWebSocketConnection();
            SK_LOGI(TAG, "WebSocket connection triggered by voice command (manual reconnect)");
            break;
        default:
            SkSmSendEvent(g_smHandler, SM_EVENT_CMD, cmd, 0, 0);
            break;
    }
    return;
}
#ifndef TESTCASE_ENABLED
static void ReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt, size_t blockSize) {
    for (int i = 0; i < memBlockCnt; i++) {
        // 不能超过配置门限，超过门限从PSRAM中分配，不能起到保留效果。所以分片分配.
        memBlock[i] = malloc(blockSize);
    }

    return;
}

static void FreeReserveMemory(uint32_t **memBlock, uint32_t memBlockCnt) {
    for (int i = 0; i < memBlockCnt; i++) {
        if (memBlock[i] != NULL) {
            free(memBlock[i]);
            memBlock[i] = NULL;
        }
    }

    return;
}
#endif

void app_main(void) {
#ifndef TESTCASE_ENABLED
    uint32_t *memResv[32];
#endif
    SK_LOGI(TAG, "Debug version at %s %s.", __DATE__, __TIME__);
    SkRledInit();
    SkRledSetEvent(SK_LED_EVENT_INIT);
    SK_OS_MODULE_MEM_STAT("start", false);
#ifndef TESTCASE_ENABLED
    ReserveMemory(memResv, ARRAY_SIZE(memResv), RESERVE_MEM_SIZE_PER_BLOCK);
    SK_OS_MODULE_MEM_STAT("Resv", true);
#endif
    ESP_ERROR_CHECK(SkBspBoardInit(16000, sizeof(uint16_t) * 8));
    SK_OS_MODULE_MEM_STAT("Bsp", true);
#ifndef TESTCASE_ENABLED
    g_smHandler = SkSmInit();
    SkConfigInit();
    SK_OS_MODULE_MEM_STAT("Config", true);
    SkWifiInit();
    SkWifiRegEventCb(SkSmOnWifiEvent);
    SK_OS_MODULE_MEM_STAT("WiFiTask", true);
    SkOtaManagerInit();
    SkOtaRegStateCallback(SkSmOtaOnStateChange);
    SK_OS_MODULE_MEM_STAT("OTA", true);
    SkOpusInit(16000, 1, 60);
    SK_OS_MODULE_MEM_STAT("OpusCodec", true);
    SkSrRegister(g_skSpeechMap, sizeof(g_skSpeechMap) / sizeof(SkSpeechMapItem), SkMainCmdProc);
    SK_OS_MODULE_MEM_STAT("Clink", false);
    SkClinkInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Clink", true);
    SkRlinkInit();
    SK_OS_MODULE_MEM_STAT("Rlink", true);
    SkWsInit();
    SkWsStart();
    // 注册WebSocket音频数据回调
    SkWsRegOnBinDataCallback(OnWsAudioData, NULL);
    SK_OS_MODULE_MEM_STAT("WebSocket", true);
    SK_OS_MODULE_MEM_STAT("Audio", false);
    SkAudioInit(sizeof(uint16_t), 960);
    SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
    SkSrSetSendFunc(SkOpusEncEnqueue);
    SkOpusEncSetCallback(SkRlinkFeedReordAudio);
    SkRlinkSetCodedDataCallback(SkOpusDecPlayRemote, SkOpusDecGetHandler());
    SkRlinkSetCodedDataEndCallback(SkOpusDecRemoteDataEnd, SkOpusDecGetHandler());
    SkPeripheralInit(g_smHandler);
    SK_OS_MODULE_MEM_STAT("Peripheral", true);
    FreeReserveMemory(memResv, ARRAY_SIZE(memResv));
    SK_OS_MODULE_MEM_STAT("Resv-End", true);

    // 配置WebSocket音频服务器（但不立即连接，等待WiFi连接成功）
    SkWsSetServerIp("************", 8768);
    SK_LOGI(TAG, "WebSocket audio server configured: ************:8766 (will connect after WiFi ready)");

    SK_OS_MODULE_MEM_STAT("WifiSta", false);
    SkSmSendEvent(g_smHandler, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_INIT_OK, 0, 0);

    // 等待WiFi连接稳定
    vTaskDelay(pdMS_TO_TICKS(15000));
    SK_OS_MODULE_MEM_STAT("WifiSta", true);

    // 自动启动WebSocket连接
    SK_LOGI(TAG, "Auto-starting WebSocket connection...");
    StartWebSocketConnection();
#else
    SkTestMain();
#endif
    return;
}
